<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静默模式鼠标指示器测试</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        .test-button:hover {
            background: #0056CC;
        }
        .test-button.danger {
            background: #FF3B30;
        }
        .test-button.danger:hover {
            background: #D70015;
        }
        .info {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #007AFF;
        }
        .warning {
            background: #fff8e1;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #ff9800;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f9f9f9;
            font-family: monospace;
        }
        .hidden-container {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔇 静默模式鼠标指示器测试</h1>
        
        <div class="info">
            <strong>测试目的：</strong><br>
            验证在没有主窗口显示的情况下，鼠标跟随指示器是否能够正常工作。这模拟了OCR Pro插件静默截图模式的实际使用场景。
        </div>

        <div class="warning">
            <strong>⚠️ 注意：</strong><br>
            点击"模拟静默模式"按钮后，主界面将被隐藏，但鼠标指示器应该仍然可见并跟随鼠标移动。
        </div>

        <div class="status" id="status">
            状态：就绪 - 等待测试
        </div>

        <div>
            <button class="test-button" onclick="testSilentMode()">🔇 模拟静默模式</button>
            <button class="test-button" onclick="testNormalMode()">🖥️ 正常模式测试</button>
            <button class="test-button danger" onclick="showInterface()">🔄 恢复界面</button>
            <button class="test-button" onclick="testFullWorkflow()">⚡ 完整流程测试</button>
        </div>

        <div class="info">
            <strong>测试步骤：</strong><br>
            1. 点击"模拟静默模式" - 界面将隐藏<br>
            2. 观察鼠标指示器是否出现并跟随鼠标<br>
            3. 等待测试完成后点击"恢复界面"<br>
            4. 检查控制台日志确认功能正常
        </div>
    </div>

    <!-- 隐藏容器用于模拟静默模式 -->
    <div class="hidden-container" id="hiddenContainer"></div>

    <!-- 引入必要的脚本 -->
    <script src="src/mouse-follow-indicator.js"></script>
    
    <!-- 模拟preload.js中的API -->
    <script>
        // 模拟ocrAPI
        window.ocrAPI = {
            getCursorPos: () => {
                // 模拟鼠标位置获取
                return { 
                    x: window.mouseX || 100, 
                    y: window.mouseY || 100 
                };
            }
        };

        // 跟踪鼠标位置
        let mouseX = 100, mouseY = 100;
        document.addEventListener('mousemove', (e) => {
            window.mouseX = e.clientX;
            window.mouseY = e.clientY;
        });
    </script>

    <script>
        let indicator = null;
        let isHidden = false;

        // 更新状态显示
        function updateStatus(message) {
            const statusEl = document.getElementById('status');
            if (statusEl) {
                statusEl.textContent = `状态：${message}`;
            }
            console.log(`[测试] ${message}`);
        }

        // 初始化指示器
        function initIndicator() {
            if (!indicator) {
                try {
                    indicator = new MouseFollowIndicator();
                    updateStatus('指示器初始化成功');
                } catch (error) {
                    updateStatus('指示器初始化失败: ' + error.message);
                    console.error('指示器初始化失败:', error);
                }
            }
        }

        // 模拟静默模式
        function testSilentMode() {
            updateStatus('开始静默模式测试...');
            
            // 初始化指示器
            initIndicator();
            
            if (!indicator) {
                updateStatus('指示器未初始化，测试失败');
                return;
            }

            // 隐藏主界面
            const container = document.querySelector('.test-container');
            if (container) {
                container.style.display = 'none';
                isHidden = true;
                updateStatus('界面已隐藏，开始指示器测试');
            }

            // 显示加载状态
            indicator.showLoading('静默模式测试中...');
            
            setTimeout(() => {
                indicator.updateText('模拟OCR识别...');
                
                setTimeout(() => {
                    indicator.showSuccess('测试完成！', 3000);
                    updateStatus('静默模式测试完成');
                }, 2000);
            }, 1500);
        }

        // 正常模式测试
        function testNormalMode() {
            updateStatus('开始正常模式测试...');
            initIndicator();
            
            if (!indicator) {
                updateStatus('指示器未初始化，测试失败');
                return;
            }

            indicator.showLoading('正常模式测试中...');
            
            setTimeout(() => {
                indicator.showSuccess('正常模式测试完成！', 2000);
                updateStatus('正常模式测试完成');
            }, 2000);
        }

        // 恢复界面
        function showInterface() {
            const container = document.querySelector('.test-container');
            if (container) {
                container.style.display = 'block';
                isHidden = false;
                updateStatus('界面已恢复');
            }
            
            if (indicator) {
                indicator.hide();
            }
        }

        // 完整流程测试
        function testFullWorkflow() {
            updateStatus('开始完整流程测试...');
            initIndicator();
            
            if (!indicator) {
                updateStatus('指示器未初始化，测试失败');
                return;
            }

            // 1. 截图阶段
            indicator.showLoading('正在截图...');
            
            setTimeout(() => {
                // 2. OCR识别阶段
                indicator.updateText('OCR识别中...');
                
                setTimeout(() => {
                    // 3. 成功完成
                    indicator.showSuccess('识别完成！', 2000);
                    updateStatus('完整流程测试完成');
                }, 3000);
            }, 1500);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('页面加载完成，准备测试');
        });

        // 监听键盘事件，按ESC恢复界面
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && isHidden) {
                showInterface();
            }
        });
    </script>
</body>
</html>
