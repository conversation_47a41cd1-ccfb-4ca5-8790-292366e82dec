<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鼠标跟随指示器测试</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        .test-button:hover {
            background: #0056CC;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .info {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #007AFF;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🖱️ 鼠标跟随状态指示器测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            点击下面的按钮来测试鼠标跟随指示器的不同状态。指示器会跟随鼠标移动并显示相应的状态信息。
        </div>

        <div class="status" id="status">
            状态：就绪
        </div>

        <div>
            <button class="test-button" onclick="testLoading()">测试加载状态</button>
            <button class="test-button" onclick="testSuccess()">测试成功状态</button>
            <button class="test-button" onclick="testError()">测试错误状态</button>
            <button class="test-button" onclick="testHide()">隐藏指示器</button>
        </div>

        <div>
            <button class="test-button" onclick="testSequence()">测试完整流程</button>
            <button class="test-button" onclick="testMouseTracking()">测试鼠标跟踪</button>
        </div>

        <div class="info">
            <strong>功能特性：</strong><br>
            • 🎯 跟随鼠标移动<br>
            • 🔄 加载动画效果<br>
            • ✅ 成功状态显示<br>
            • ❌ 错误状态显示<br>
            • 🎨 透明背景和模糊效果<br>
            • ⏰ 自动隐藏功能
        </div>
    </div>

    <!-- 引入鼠标跟随指示器 -->
    <script src="src/mouse-follow-indicator.js"></script>
    
    <!-- 模拟preload.js中的API -->
    <script>
        // 模拟ocrAPI
        window.ocrAPI = {
            getCursorPos: () => {
                // 返回当前鼠标位置的模拟数据
                return { x: 100, y: 100 };
            }
        };
    </script>

    <script>
        let indicator = null;

        // 初始化指示器
        function initIndicator() {
            if (!indicator) {
                indicator = new MouseFollowIndicator();
                updateStatus('指示器已初始化');
            }
        }

        // 更新状态显示
        function updateStatus(message) {
            document.getElementById('status').textContent = `状态：${message}`;
        }

        // 测试加载状态
        function testLoading() {
            initIndicator();
            indicator.showLoading('正在处理...');
            updateStatus('显示加载状态');
        }

        // 测试成功状态
        function testSuccess() {
            initIndicator();
            indicator.showSuccess('操作成功！', 3000);
            updateStatus('显示成功状态');
        }

        // 测试错误状态
        function testError() {
            initIndicator();
            indicator.showError('操作失败！', 3000);
            updateStatus('显示错误状态');
        }

        // 隐藏指示器
        function testHide() {
            if (indicator) {
                indicator.hide();
                updateStatus('指示器已隐藏');
            }
        }

        // 测试完整流程
        function testSequence() {
            initIndicator();
            updateStatus('开始完整流程测试');
            
            // 1. 显示加载状态
            indicator.showLoading('正在截图...');
            
            setTimeout(() => {
                // 2. 更新为识别状态
                indicator.updateText('OCR识别中...');
                
                setTimeout(() => {
                    // 3. 显示成功状态
                    indicator.showSuccess('识别完成！', 2000);
                    updateStatus('完整流程测试完成');
                }, 2000);
            }, 1500);
        }

        // 测试鼠标跟踪
        function testMouseTracking() {
            initIndicator();
            indicator.showLoading('移动鼠标测试跟踪效果');
            updateStatus('鼠标跟踪测试中 - 请移动鼠标');
            
            setTimeout(() => {
                indicator.showSuccess('跟踪测试完成', 2000);
                updateStatus('鼠标跟踪测试完成');
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('页面加载完成，点击按钮开始测试');
        });
    </script>
</body>
</html>
