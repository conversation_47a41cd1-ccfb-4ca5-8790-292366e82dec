// 鼠标跟随状态指示器组件
class MouseFollowIndicator {
    constructor() {
        this.indicator = null;
        this.isVisible = false;
        this.currentState = 'hidden'; // hidden, loading, success, error
        this.mousePosition = { x: 0, y: 0 };
        this.animationFrame = null;
        this.hideTimeout = null;
        this.isTracking = false;
        this.systemWindow = null; // 系统级窗口引用
        this.isSystemMode = false; // 是否使用系统级显示

        // 尝试创建指示器元素
        this.initializeIndicator();

        // 绑定鼠标跟踪事件
        this.bindMouseTracking();
    }

    // 初始化指示器
    initializeIndicator() {
        // 首先尝试系统级显示（uTools API）
        if (this.tryCreateSystemIndicator()) {
            this.isSystemMode = true;
            console.log('[鼠标指示器] 使用系统级显示模式');
        } else {
            // 回退到DOM模式
            this.createDOMIndicator();
            this.isSystemMode = false;
            console.log('[鼠标指示器] 使用DOM显示模式');
        }
    }

    // 尝试创建系统级指示器
    tryCreateSystemIndicator() {
        try {
            // 暂时禁用系统级窗口创建，因为可能需要特殊权限
            // 改为使用更强大的DOM方案，确保在静默模式下也能工作
            return false;
        } catch (error) {
            console.warn('[鼠标指示器] 系统级显示创建失败:', error);
            return false;
        }
    }

    // 创建系统级窗口
    createSystemWindow() {
        try {
            // 创建一个透明的系统级窗口用于显示指示器
            const windowOptions = {
                width: 200,
                height: 80,
                frame: false,
                transparent: true,
                alwaysOnTop: true,
                skipTaskbar: true,
                resizable: false,
                movable: false,
                minimizable: false,
                maximizable: false,
                closable: false,
                focusable: false,
                show: false,
                webPreferences: {
                    nodeIntegration: true,
                    contextIsolation: false
                }
            };

            this.systemWindow = utools.createBrowserWindow(windowOptions);

            if (this.systemWindow) {
                // 加载指示器HTML内容
                const indicatorHTML = this.getIndicatorHTML();
                this.systemWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(indicatorHTML)}`);

                // 设置窗口属性
                this.systemWindow.setIgnoreMouseEvents(true);
                this.systemWindow.setAlwaysOnTop(true, 'screen-saver');

                return true;
            }
            return false;
        } catch (error) {
            console.error('[鼠标指示器] 创建系统窗口失败:', error);
            return false;
        }
    }

    // 创建DOM指示器（回退方案）
    createDOMIndicator() {
        try {
            // 创建或确保有可用的容器
            this.ensureContainer();

            if (!this.container) {
                console.warn('[鼠标指示器] 无法创建容器，延迟重试');
                setTimeout(() => this.createDOMIndicator(), 100);
                return;
            }

            this.indicator = document.createElement('div');
            this.indicator.className = 'mouse-follow-indicator';
            this.indicator.innerHTML = `
                <div class="indicator-content">
                    <div class="indicator-icon">
                        <div class="loading-spinner"></div>
                        <div class="success-icon">✓</div>
                        <div class="error-icon">✗</div>
                    </div>
                    <div class="indicator-text"></div>
                </div>
            `;

            // 设置初始样式
            this.indicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                z-index: 999999;
                pointer-events: none;
                opacity: 0;
                transform: translate(-50%, -50%);
                transition: opacity 0.3s ease;
            `;

            // 添加到容器
            this.container.appendChild(this.indicator);
            console.log('[鼠标指示器] DOM指示器创建成功');
        } catch (error) {
            console.error('[鼠标指示器] 创建DOM指示器失败:', error);
        }
    }

    // 确保有可用的容器
    ensureContainer() {
        try {
            // 优先使用document.body
            if (document && document.body) {
                this.container = document.body;
                return;
            }

            // 如果body不可用，尝试创建一个临时容器
            if (document) {
                // 等待DOM加载完成
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        this.ensureContainer();
                        this.createDOMIndicator();
                    });
                    return;
                }

                // 尝试创建body
                if (!document.body) {
                    const body = document.createElement('body');
                    if (document.documentElement) {
                        document.documentElement.appendChild(body);
                        this.container = body;
                    }
                }
            }

            // 最后的备用方案：创建一个独立的容器
            if (!this.container && typeof window !== 'undefined') {
                console.warn('[鼠标指示器] 使用备用容器方案');
                // 这种情况下指示器可能无法显示，但至少不会报错
                this.container = null;
            }
        } catch (error) {
            console.error('[鼠标指示器] 确保容器失败:', error);
            this.container = null;
        }
    }

    // 获取指示器HTML内容（用于系统窗口）
    getIndicatorHTML() {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
        }

        .mouse-follow-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 999999;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .indicator-content {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.85);
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
            min-height: 32px;
        }

        .indicator-icon {
            position: relative;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .indicator-icon > * {
            position: absolute;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mouse-follow-indicator.loading .loading-spinner {
            opacity: 1;
        }

        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .mouse-follow-indicator.success .success-icon {
            opacity: 1;
        }

        .mouse-follow-indicator.success .indicator-content {
            background: rgba(34, 197, 94, 0.9);
        }

        .success-icon {
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .mouse-follow-indicator.error .error-icon {
            opacity: 1;
        }

        .mouse-follow-indicator.error .indicator-content {
            background: rgba(239, 68, 68, 0.9);
        }

        .error-icon {
            color: white;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="mouse-follow-indicator" id="indicator">
        <div class="indicator-content">
            <div class="indicator-icon">
                <div class="loading-spinner"></div>
                <div class="success-icon">✓</div>
                <div class="error-icon">✗</div>
            </div>
            <div class="indicator-text" id="indicator-text"></div>
        </div>
    </div>

    <script>
        // 系统窗口模式下的控制接口
        window.indicatorControl = {
            show: () => {
                const indicator = document.getElementById('indicator');
                if (indicator) {
                    indicator.style.opacity = '1';
                }
            },
            hide: () => {
                const indicator = document.getElementById('indicator');
                if (indicator) {
                    indicator.style.opacity = '0';
                }
            },
            setState: (state, text) => {
                const indicator = document.getElementById('indicator');
                const textElement = document.getElementById('indicator-text');
                if (indicator && textElement) {
                    indicator.className = 'mouse-follow-indicator ' + state;
                    textElement.textContent = text || '';
                }
            }
        };
    </script>
</body>
</html>`;
    }

    // 绑定鼠标跟踪事件
    bindMouseTracking() {
        if (this.isSystemMode) {
            // 系统模式：使用uTools API跟踪鼠标
            this.trackMouse = () => {
                if (!this.isTracking) return;

                try {
                    const cursorPos = window.ocrAPI?.getCursorPos?.();
                    if (cursorPos && cursorPos.x !== undefined && cursorPos.y !== undefined) {
                        this.mousePosition = { x: cursorPos.x, y: cursorPos.y };
                        this.updatePosition();
                    }
                } catch (error) {
                    console.warn('[鼠标指示器] 获取鼠标位置失败:', error);
                }

                if (this.isTracking) {
                    this.animationFrame = requestAnimationFrame(this.trackMouse);
                }
            };
        } else {
            // DOM模式：使用鼠标事件
            this.mouseMoveHandler = (e) => {
                this.mousePosition.x = e.clientX;
                this.mousePosition.y = e.clientY;
                this.updatePosition();
            };
        }
    }

    // 开始跟踪鼠标
    startTracking() {
        if (this.isTracking) return;

        this.isTracking = true;

        if (this.isSystemMode) {
            // 系统模式：启动动画帧跟踪
            this.getCurrentMousePosition();
            this.trackMouse();
        } else {
            // DOM模式：监听鼠标事件
            if (document) {
                document.addEventListener('mousemove', this.mouseMoveHandler);
            }
            this.getCurrentMousePosition();
        }
    }

    // 停止跟踪鼠标
    stopTracking() {
        if (!this.isTracking) return;

        this.isTracking = false;

        if (this.isSystemMode) {
            // 系统模式：取消动画帧
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
                this.animationFrame = null;
            }
        } else {
            // DOM模式：移除事件监听
            if (document && this.mouseMoveHandler) {
                document.removeEventListener('mousemove', this.mouseMoveHandler);
            }
        }
    }

    // 获取当前鼠标位置（用于初始化）
    getCurrentMousePosition() {
        // 尝试从utools API获取鼠标位置
        if (window.ocrAPI && window.ocrAPI.getCursorPos) {
            try {
                const pos = window.ocrAPI.getCursorPos();
                if (pos && pos.x !== undefined && pos.y !== undefined) {
                    this.mousePosition.x = pos.x;
                    this.mousePosition.y = pos.y;
                    this.updatePosition();
                    return;
                }
            } catch (error) {
                console.warn('获取鼠标位置失败:', error);
            }
        }
        
        // 备用方案：使用屏幕中心位置
        this.mousePosition.x = window.screen.width / 2;
        this.mousePosition.y = window.screen.height / 2;
        this.updatePosition();
    }

    // 更新指示器位置
    updatePosition() {
        if (!this.isVisible) return;

        // 添加偏移量，避免遮挡鼠标
        const offsetX = 20;
        const offsetY = -20;

        const x = this.mousePosition.x + offsetX;
        const y = this.mousePosition.y + offsetY;

        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：移动窗口位置
            try {
                this.systemWindow.setPosition(x, y);
            } catch (error) {
                console.warn('[鼠标指示器] 更新系统窗口位置失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：更新元素位置
            this.indicator.style.left = `${x}px`;
            this.indicator.style.top = `${y}px`;
        }
    }

    // 显示加载状态
    showLoading(text = 'OCR识别中...') {
        this.currentState = 'loading';
        this.startTracking();

        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：通过窗口控制
            try {
                this.systemWindow.webContents.executeJavaScript(`
                    if (window.indicatorControl) {
                        window.indicatorControl.setState('loading', '${text}');
                        window.indicatorControl.show();
                    }
                `);
            } catch (error) {
                console.warn('[鼠标指示器] 系统模式显示加载状态失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：直接操作元素
            const textElement = this.indicator.querySelector('.indicator-text');
            if (textElement) {
                textElement.textContent = text;
            }
            this.indicator.className = 'mouse-follow-indicator loading';
        }

        // 显示指示器
        this.show();
    }

    // 显示成功状态
    showSuccess(text = '识别完成', duration = 2000) {
        this.currentState = 'success';

        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：通过窗口控制
            try {
                this.systemWindow.webContents.executeJavaScript(`
                    if (window.indicatorControl) {
                        window.indicatorControl.setState('success', '${text}');
                        window.indicatorControl.show();
                    }
                `);
            } catch (error) {
                console.warn('[鼠标指示器] 系统模式显示成功状态失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：直接操作元素
            const textElement = this.indicator.querySelector('.indicator-text');
            if (textElement) {
                textElement.textContent = text;
            }
            this.indicator.className = 'mouse-follow-indicator success';
        }

        // 显示指示器
        this.show();

        // 自动隐藏
        if (duration > 0) {
            this.hideTimeout = setTimeout(() => {
                this.hide();
            }, duration);
        }
    }

    // 显示错误状态
    showError(text = '识别失败', duration = 3000) {
        this.currentState = 'error';
        
        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：通过窗口控制
            try {
                this.systemWindow.webContents.executeJavaScript(`
                    if (window.indicatorControl) {
                        window.indicatorControl.setState('error', '${text}');
                        window.indicatorControl.show();
                    }
                `);
            } catch (error) {
                console.warn('[鼠标指示器] 系统模式显示错误状态失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：直接操作元素
            const textElement = this.indicator.querySelector('.indicator-text');
            if (textElement) {
                textElement.textContent = text;
            }
            this.indicator.className = 'mouse-follow-indicator error';
        }

        // 显示指示器
        this.show();

        // 自动隐藏
        if (duration > 0) {
            this.hideTimeout = setTimeout(() => {
                this.hide();
            }, duration);
        }
    }

    // 显示指示器
    show() {
        this.isVisible = true;
        this.updatePosition();

        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：显示窗口
            try {
                this.systemWindow.show();
                this.systemWindow.webContents.executeJavaScript(`
                    if (window.indicatorControl) {
                        window.indicatorControl.show();
                    }
                `);
            } catch (error) {
                console.warn('[鼠标指示器] 系统模式显示失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：设置透明度
            requestAnimationFrame(() => {
                this.indicator.style.opacity = '1';
            });
        }
    }

    // 隐藏指示器
    hide() {
        // 清除定时器
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }

        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：隐藏窗口
            try {
                this.systemWindow.webContents.executeJavaScript(`
                    if (window.indicatorControl) {
                        window.indicatorControl.hide();
                    }
                `);
                setTimeout(() => {
                    this.systemWindow.hide();
                }, 300);
            } catch (error) {
                console.warn('[鼠标指示器] 系统模式隐藏失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：设置透明度
            this.indicator.style.opacity = '0';
        }

        // 延迟停止跟踪
        setTimeout(() => {
            this.isVisible = false;
            this.currentState = 'hidden';
            this.stopTracking();
        }, 300);
    }

    // 更新文本
    updateText(text) {
        if (this.isSystemMode && this.systemWindow) {
            // 系统模式：通过窗口控制
            try {
                this.systemWindow.webContents.executeJavaScript(`
                    const textElement = document.getElementById('indicator-text');
                    if (textElement) {
                        textElement.textContent = '${text}';
                    }
                `);
            } catch (error) {
                console.warn('[鼠标指示器] 系统模式更新文本失败:', error);
            }
        } else if (this.indicator) {
            // DOM模式：直接操作元素
            const textElement = this.indicator.querySelector('.indicator-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }
    }

    // 销毁指示器
    destroy() {
        this.hide();
        this.stopTracking();
        
        if (this.indicator && this.indicator.parentNode) {
            this.indicator.parentNode.removeChild(this.indicator);
        }
        
        this.indicator = null;
    }

    // 检查是否可见
    isIndicatorVisible() {
        return this.isVisible;
    }

    // 获取当前状态
    getCurrentState() {
        return this.currentState;
    }
}

// 导出类
window.MouseFollowIndicator = MouseFollowIndicator;
