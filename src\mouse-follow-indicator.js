// 鼠标跟随状态指示器组件
class MouseFollowIndicator {
    constructor() {
        this.indicator = null;
        this.isVisible = false;
        this.currentState = 'hidden'; // hidden, loading, success, error
        this.mousePosition = { x: 0, y: 0 };
        this.animationFrame = null;
        this.hideTimeout = null;
        this.isTracking = false;
        
        // 创建指示器元素
        this.createIndicator();
        
        // 绑定鼠标跟踪事件
        this.bindMouseTracking();
    }

    // 创建指示器DOM元素
    createIndicator() {
        this.indicator = document.createElement('div');
        this.indicator.className = 'mouse-follow-indicator';
        this.indicator.innerHTML = `
            <div class="indicator-content">
                <div class="indicator-icon">
                    <div class="loading-spinner"></div>
                    <div class="success-icon">✓</div>
                    <div class="error-icon">✗</div>
                </div>
                <div class="indicator-text"></div>
            </div>
        `;
        
        // 设置初始样式
        this.indicator.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            z-index: 999999;
            pointer-events: none;
            opacity: 0;
            transform: translate(-50%, -50%);
            transition: opacity 0.3s ease;
        `;
        
        // 添加到body
        document.body.appendChild(this.indicator);
    }

    // 绑定鼠标跟踪事件
    bindMouseTracking() {
        this.mouseMoveHandler = (e) => {
            this.mousePosition.x = e.clientX;
            this.mousePosition.y = e.clientY;
            this.updatePosition();
        };
    }

    // 开始跟踪鼠标
    startTracking() {
        if (this.isTracking) return;
        
        this.isTracking = true;
        document.addEventListener('mousemove', this.mouseMoveHandler);
        
        // 获取当前鼠标位置
        this.getCurrentMousePosition();
    }

    // 停止跟踪鼠标
    stopTracking() {
        if (!this.isTracking) return;
        
        this.isTracking = false;
        document.removeEventListener('mousemove', this.mouseMoveHandler);
        
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }

    // 获取当前鼠标位置（用于初始化）
    getCurrentMousePosition() {
        // 尝试从utools API获取鼠标位置
        if (window.ocrAPI && window.ocrAPI.getCursorPos) {
            try {
                const pos = window.ocrAPI.getCursorPos();
                if (pos && pos.x !== undefined && pos.y !== undefined) {
                    this.mousePosition.x = pos.x;
                    this.mousePosition.y = pos.y;
                    this.updatePosition();
                    return;
                }
            } catch (error) {
                console.warn('获取鼠标位置失败:', error);
            }
        }
        
        // 备用方案：使用屏幕中心位置
        this.mousePosition.x = window.screen.width / 2;
        this.mousePosition.y = window.screen.height / 2;
        this.updatePosition();
    }

    // 更新指示器位置
    updatePosition() {
        if (!this.indicator || !this.isVisible) return;
        
        // 添加偏移量，避免遮挡鼠标
        const offsetX = 20;
        const offsetY = -20;
        
        const x = this.mousePosition.x + offsetX;
        const y = this.mousePosition.y + offsetY;
        
        this.indicator.style.left = `${x}px`;
        this.indicator.style.top = `${y}px`;
    }

    // 显示加载状态
    showLoading(text = 'OCR识别中...') {
        this.currentState = 'loading';
        this.startTracking();
        
        // 更新内容
        const textElement = this.indicator.querySelector('.indicator-text');
        if (textElement) {
            textElement.textContent = text;
        }
        
        // 更新样式
        this.indicator.className = 'mouse-follow-indicator loading';
        
        // 显示指示器
        this.show();
    }

    // 显示成功状态
    showSuccess(text = '识别完成', duration = 2000) {
        this.currentState = 'success';
        
        // 更新内容
        const textElement = this.indicator.querySelector('.indicator-text');
        if (textElement) {
            textElement.textContent = text;
        }
        
        // 更新样式
        this.indicator.className = 'mouse-follow-indicator success';
        
        // 自动隐藏
        if (duration > 0) {
            this.hideTimeout = setTimeout(() => {
                this.hide();
            }, duration);
        }
    }

    // 显示错误状态
    showError(text = '识别失败', duration = 3000) {
        this.currentState = 'error';
        
        // 更新内容
        const textElement = this.indicator.querySelector('.indicator-text');
        if (textElement) {
            textElement.textContent = text;
        }
        
        // 更新样式
        this.indicator.className = 'mouse-follow-indicator error';
        
        // 自动隐藏
        if (duration > 0) {
            this.hideTimeout = setTimeout(() => {
                this.hide();
            }, duration);
        }
    }

    // 显示指示器
    show() {
        if (!this.indicator) return;
        
        this.isVisible = true;
        this.updatePosition();
        
        // 使用requestAnimationFrame确保样式更新
        requestAnimationFrame(() => {
            this.indicator.style.opacity = '1';
        });
    }

    // 隐藏指示器
    hide() {
        if (!this.indicator) return;
        
        // 清除定时器
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }
        
        this.indicator.style.opacity = '0';
        
        // 延迟停止跟踪
        setTimeout(() => {
            this.isVisible = false;
            this.currentState = 'hidden';
            this.stopTracking();
        }, 300);
    }

    // 更新文本
    updateText(text) {
        const textElement = this.indicator.querySelector('.indicator-text');
        if (textElement) {
            textElement.textContent = text;
        }
    }

    // 销毁指示器
    destroy() {
        this.hide();
        this.stopTracking();
        
        if (this.indicator && this.indicator.parentNode) {
            this.indicator.parentNode.removeChild(this.indicator);
        }
        
        this.indicator = null;
    }

    // 检查是否可见
    isIndicatorVisible() {
        return this.isVisible;
    }

    // 获取当前状态
    getCurrentState() {
        return this.currentState;
    }
}

// 导出类
window.MouseFollowIndicator = MouseFollowIndicator;
