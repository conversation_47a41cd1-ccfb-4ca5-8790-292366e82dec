// uTools插件预加载脚本

// 保存当前功能代码和payload
let currentFeatureCode = null;
let currentPayload = null;

// 监听uTools进入事件
if (typeof utools !== 'undefined') {
    try {
        // 通用事件处理函数
        const handlePluginEvent = ({ code, type, payload }) => {
            currentFeatureCode = code;
            currentPayload = payload;
            window.currentFeatureCode = code;
            window.currentPayload = payload;

            // 触发自定义事件通知主程序
            window.dispatchEvent(new CustomEvent('utools-plugin-enter', {
                detail: { code, type, payload }
            }));
        };

        // 设置插件进入监听
        utools.onPluginEnter(handlePluginEvent);

        // 监听主窗口推送事件（用于处理图片匹配）
        utools.onMainPush((eventData) => {
            handlePluginEvent(eventData);
            return true; // 返回true表示进入插件应用
        });

        // 监听插件退出
        utools.onPluginOut(() => {
            // 根据配置决定是否清空相应页面内容
            if (window.ocrPlugin && window.ocrPlugin.uiManager) {
                const currentView = window.ocrPlugin.uiManager.currentView;
                const config = window.ocrPlugin.configManager?.getConfig();

                if (currentView === 'translate' && config?.ui?.autoCleanTranslate) {
                    window.ocrPlugin.uiManager.clearTranslateContentSilently();
                } else if (currentView === 'main' && config?.ui?.autoCleanOCR) {
                    window.ocrPlugin.uiManager.clearOCRContentSilently();
                } else if (currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
                    window.ocrPlugin.uiManager.clearImageTranslateContentSilently();
                }
            }

            [currentFeatureCode, currentPayload, window.currentFeatureCode, window.currentPayload] = [null, null, null, null];
        });
    } catch (error) {
        console.error('设置uTools事件监听失败:', error);
    }
} else {
    console.warn('uTools API 不可用，可能在开发环境中运行');
}

// 暴露API给渲染进程
window.ocrAPI = {
    // 屏幕截图
    screenCapture: (callback) => {
        if (typeof utools !== 'undefined' && utools.screenCapture) {
            let screenshotCompleted = false;
            let focusListener = null;

            // 完成截图的通用函数
            const completeScreenshot = (image, reason) => {
                if (screenshotCompleted) return;

                screenshotCompleted = true;

                // 清理监听器
                if (focusListener) {
                    window.removeEventListener('focus', focusListener);
                    focusListener = null;
                }

                if (callback) {
                    callback(image);
                }
            };

            // 监听窗口焦点变化来检测用户取消截图
            focusListener = () => {
                // 延迟一点检查，确保不是正常的焦点切换
                setTimeout(() => {
                    if (!screenshotCompleted) {
                        completeScreenshot(null, '用户取消');
                    }
                }, 100);
            };

            window.addEventListener('focus', focusListener);

            try {
                utools.screenCapture((image) => {
                    if (image) {
                        completeScreenshot(image, 'uTools回调');
                    } else {
                        completeScreenshot(null, 'uTools回调');
                    }
                });
            } catch (error) {
                console.error('截图API调用失败:', error);
                completeScreenshot(null, 'API调用失败');
            }
        } else {
            console.error('🔴 screenCapture API 不可用');
            if (callback) callback(null);
        }
    },

    // 安全调用utools API的通用方法
    safeCall: (apiName, ...args) => {
        if (typeof utools !== 'undefined' && utools[apiName]) {
            try {
                return utools[apiName](...args);
            } catch (error) {
                console.error(`调用${apiName} API失败:`, error);
                return null;
            }
        } else {
            // 在开发环境中，这是正常情况，不需要显示错误
            if (apiName === 'db.get' || apiName === 'db.put' || apiName === 'db.remove') {
                // 在开发环境中完全静默，不显示任何日志
                return null;
            } else {
                console.error(`${apiName} API 不可用`);
            }
            return null;
        }
    },

    // 数据库操作
    db: {
        get: (id) => window.ocrAPI.safeCall('db.get', id) || utools?.db?.get(id),
        put: (doc) => window.ocrAPI.safeCall('db.put', doc) || utools?.db?.put(doc),
        remove: (id) => window.ocrAPI.safeCall('db.remove', id) || utools?.db?.remove(id)
    },

    // 复制到剪贴板
    copyText: (text) => window.ocrAPI.safeCall('copyText', text),

    // 隐藏窗口
    hideMainWindow: () => window.ocrAPI.safeCall('hideMainWindow'),

    // 显示窗口
    showMainWindow: () => window.ocrAPI.safeCall('showMainWindow'),

    // 获取当前功能代码
    getCurrentFeature: () => {
        // 优先从保存的功能代码获取
        if (currentFeatureCode) {
            return currentFeatureCode;
        }
        
        if (window.currentFeatureCode) {
            return window.currentFeatureCode;
        }
        
        // 尝试从uTools API获取
        try {
            // 检查是否有payload，根据payload类型推断功能
            let payload = currentPayload || window.currentPayload;
            
            // 只有在uTools API可用且getPayload方法存在时才调用
            if (!payload && typeof utools !== 'undefined' && typeof utools.getPayload === 'function') {
                payload = utools.getPayload();
            }
            
            if (payload) {
                if (payload.type === 'img') {
                    currentFeatureCode = 'ocr-clipboard';
                    return 'ocr-clipboard';
                } else if (payload.type === 'files') {
                    currentFeatureCode = 'ocr-files';
                    return 'ocr-files';
                } else if (payload.text) {
                    // 文本匹配指令
                    currentFeatureCode = 'text-translate';
                    return 'text-translate';
                }
            }
        } catch (error) {
            console.error('获取payload失败:', error);
        }
        
        // 默认返回null，让主程序处理
        return null;
    },

    // 获取payload数据
    getPayload: () => {
        try {
            let payload = currentPayload || window.currentPayload;

            // 只有在uTools API可用且getPayload方法存在时才调用
            if (!payload && typeof utools !== 'undefined' && typeof utools.getPayload === 'function') {
                payload = utools.getPayload();
            }


            return payload || null;
        } catch (error) {
            console.error('获取payload失败:', error);
            return null;
        }
    },

    // 读取文件（如果uTools支持）
    readFile: (filePath) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.readFile === 'function') {
                return utools.readFile(filePath);
            } else {
                return null;
            }
        } catch (error) {
            console.error('读取文件失败:', error);
            return null;
        }
    },

    // 检查文件是否存在（如果uTools支持）
    fileExists: (filePath) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.fileExists === 'function') {
                return utools.fileExists(filePath);
            } else if (typeof utools !== 'undefined' && typeof utools.readFile === 'function') {
                // 尝试读取文件来检查是否存在
                try {
                    const result = utools.readFile(filePath);
                    return result !== null && result !== undefined;
                } catch (e) {
                    return false;
                }
            } else {
                return false;
            }
        } catch (error) {
            console.error('检查文件存在性失败:', error);
            return false;
        }
    },

    // 显示文件选择对话框
    showFileDialog: (callback) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.showOpenDialog === 'function') {
                const result = utools.showOpenDialog({
                    title: '选择图片文件',
                    filters: [
                        { name: '图片文件', extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'tiff', 'svg'] }
                    ],
                    properties: ['openFile']
                });
                if (callback) callback(result);
                return result;
            } else {
                if (callback) callback(null);
                return null;
            }
        } catch (error) {
            console.error('显示文件选择对话框失败:', error);
            if (callback) callback(null);
            return null;
        }
    },

    // 显示系统通知
    showNotification: (message, type = 'info') => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.showNotification === 'function') {
                // 使用uTools的通知API
                utools.showNotification(message);
            } else {
                // 备用方案：使用浏览器通知API
                if ('Notification' in window) {
                    if (Notification.permission === 'granted') {
                        new Notification('OCR Pro', {
                            body: message,
                            icon: 'assets/logo.png'
                        });
                    } else if (Notification.permission !== 'denied') {
                        Notification.requestPermission().then(permission => {
                            if (permission === 'granted') {
                                new Notification('OCR Pro', {
                                    body: message,
                                    icon: 'assets/logo.png'
                                });
                            }
                        });
                    }
                } else {
                    // 最后的备用方案：控制台输出
                    console.log(`[OCR Pro] ${type.toUpperCase()}: ${message}`);
                }
            }
        } catch (error) {
            console.error('显示通知失败:', error);
            // 最后的备用方案：控制台输出
            console.log(`[OCR Pro] ${type.toUpperCase()}: ${message}`);
        }
    },

    // 获取鼠标位置
    getCursorPos: () => {
        try {
            // 优先使用getCursorScreenPoint（屏幕坐标）
            if (typeof utools !== 'undefined' && typeof utools.getCursorScreenPoint === 'function') {
                const pos = utools.getCursorScreenPoint();
                if (pos && typeof pos.x === 'number' && typeof pos.y === 'number') {
                    return pos;
                }
            }

            // 备用方案：使用getCursorPos
            if (typeof utools !== 'undefined' && typeof utools.getCursorPos === 'function') {
                const pos = utools.getCursorPos();
                if (pos && typeof pos.x === 'number' && typeof pos.y === 'number') {
                    return pos;
                }
            }

            // 最后备用方案：使用screen API
            if (typeof utools !== 'undefined' && typeof utools.getDisplays === 'function') {
                const displays = utools.getDisplays();
                if (displays && displays.length > 0) {
                    const primaryDisplay = displays.find(d => d.primary) || displays[0];
                    return {
                        x: Math.floor(primaryDisplay.bounds.width / 2),
                        y: Math.floor(primaryDisplay.bounds.height / 2)
                    };
                }
            }
        } catch (error) {
            console.warn('获取鼠标位置失败:', error);
        }

        // 最终备用方案：返回屏幕中心
        return {
            x: Math.floor((window.screen?.width || 1920) / 2),
            y: Math.floor((window.screen?.height || 1080) / 2)
        };
    }
};
